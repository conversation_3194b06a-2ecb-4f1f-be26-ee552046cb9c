from flask import Blueprint, render_template, redirect, url_for, flash, request, current_app, jsonify
from flask_login import login_required, current_user
from app import db
from app.models.image import Image
from app.models.timeline import Timeline
from app.models.ai_job import AIGenerationJob, AIJobStatus
from app.utils.async_ai_processor import Async<PERSON>IPro<PERSON>or
from werkzeug.utils import secure_filename
from flask_wtf import FlaskForm
from flask_wtf.file import FileField, FileRequired, FileAllowed
from wtforms import StringField, TextAreaField, TimeField, SubmitField, HiddenField
from wtforms.validators import DataRequired
import os
import uuid
from datetime import datetime

image_bp = Blueprint('image', __name__, url_prefix='/image')

class ImageUploadForm(FlaskForm):
    image = FileField('Image', validators=[
        FileRequired(),
        FileAllowed(['jpg', 'jpeg', 'png', 'gif'], 'Images only!')
    ])
    timeline_id = HiddenField('Timeline ID', validators=[DataRequired()])
    submit = SubmitField('Upload Image')

class ImageProcessForm(FlaskForm):
    ai_content = TextAreaField('AI Content')
    time_mark = TimeField('Time Mark', format='%H:%M')
    submit = SubmitField('Save')

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in {'png', 'jpg', 'jpeg', 'gif'}

def save_image(file):
    if file and allowed_file(file.filename):
        filename = secure_filename(file.filename)
        # Generate a unique filename to prevent overwriting
        unique_filename = f"{uuid.uuid4().hex}_{filename}"
        upload_folder = current_app.config['UPLOAD_FOLDER']

        # Debug information
        print(f"Upload folder: {upload_folder}")
        print(f"File to save: {unique_filename}")

        # Ensure the upload folder exists
        os.makedirs(upload_folder, exist_ok=True)

        file_path = os.path.join(upload_folder, unique_filename)
        print(f"Full file path: {file_path}")

        try:
            file.save(file_path)
            print(f"File saved successfully at: {file_path}")
            return unique_filename
        except Exception as e:
            print(f"Error saving file: {str(e)}")
            return None
    return None

@image_bp.route('/upload', methods=['GET', 'POST'])
@login_required
def upload():
    form = ImageUploadForm()

    # Get timeline_id from query parameter
    timeline_id = request.args.get('timeline_id', type=int)
    if timeline_id:
        form.timeline_id.data = timeline_id

    if form.validate_on_submit():
        print(f"Form validated, timeline_id: {form.timeline_id.data}")
        timeline = Timeline.query.get_or_404(form.timeline_id.data)

        # Check if user is authorized to add images to this timeline
        if timeline.user_id != current_user.id and not current_user.is_admin:
            flash('You are not authorized to add images to this timeline', 'danger')
            return redirect(url_for('timeline.view', id=timeline.id))

        file = form.image.data
        print(f"Received file: {file.filename}, mimetype: {file.mimetype}")

        # Ensure the upload folder exists
        upload_folder = current_app.config['UPLOAD_FOLDER']
        os.makedirs(upload_folder, exist_ok=True)
        print(f"Upload folder: {upload_folder}")

        filename = save_image(file)
        print(f"Saved image filename: {filename}")

        if filename:
            # Create new image record
            image = Image(
                filename=filename,
                original_filename=secure_filename(file.filename),
                timeline_id=timeline.id,
                user_id=current_user.id
            )
            db.session.add(image)
            db.session.commit()
            print(f"Created image record with ID: {image.id}")

            # Verify the file exists
            file_path = os.path.join(upload_folder, filename)
            if os.path.exists(file_path):
                print(f"File exists at: {file_path}")
            else:
                print(f"WARNING: File does not exist at: {file_path}")

            flash('Image uploaded successfully!', 'success')
            return redirect(url_for('image.process', id=image.id))
        else:
            flash('Error saving image', 'danger')

    return render_template('image/upload.html', form=form)

@image_bp.route('/<int:id>/process', methods=['GET', 'POST'])
@login_required
def process(id):
    image = Image.query.get_or_404(id)

    # Check if user is authorized to process this image
    if image.user_id != current_user.id and not current_user.is_admin:
        flash('You are not authorized to process this image', 'danger')
        return redirect(url_for('timeline.view', id=image.timeline_id))

    form = ImageProcessForm()

    if form.validate_on_submit():
        # In a real application, this would call an AI service to process the image
        # For now, we'll just save the user-provided content

        image.ai_content = form.ai_content.data
        image.time_mark = form.time_mark.data
        db.session.commit()

        flash('Image processed successfully!', 'success')
        return redirect(url_for('timeline.view', id=image.timeline_id))

    # Fill form with existing data
    if request.method == 'GET' and image.ai_content:
        form.ai_content.data = image.ai_content
        form.time_mark.data = image.time_mark

    # For demo purposes, generate some mock AI content if none exists
    if not form.ai_content.data:
        form.ai_content.data = f"This is a screenshot taken on {datetime.now().strftime('%Y-%m-%d')}. It appears to show a work-related task."

    return render_template('image/process.html', form=form, image=image)

@image_bp.route('/<int:id>/generate-ai-content', methods=['POST'])
@login_required
def generate_ai_content(id):
    """Generate AI content for an image with simulated async behavior"""
    image = Image.query.get_or_404(id)

    # Check if user is authorized to process this image
    if image.user_id != current_user.id and not current_user.is_admin:
        return jsonify({'error': 'You are not authorized to process this image'}), 403

    try:
        # Check if there's already a running job for this image
        existing_job = AIGenerationJob.query.filter_by(
            image_id=image.id
        ).filter(
            AIGenerationJob.status.in_([AIJobStatus.PENDING, AIJobStatus.PROCESSING])
        ).first()

        if existing_job:
            return jsonify({
                'success': True,
                'job_id': existing_job.id,
                'message': 'AI content generation already in progress',
                'status_url': url_for('image.get_ai_job_status', job_id=existing_job.id),
                'already_running': True
            })

        # Cancel any old failed/timeout jobs for this image to keep database clean
        old_jobs = AIGenerationJob.query.filter_by(
            image_id=image.id
        ).filter(
            AIGenerationJob.status.in_([AIJobStatus.FAILED, AIJobStatus.TIMEOUT])
        ).all()

        for old_job in old_jobs:
            db.session.delete(old_job)

        # Get the full path to the image file
        image_path = os.path.join(current_app.config['UPLOAD_FOLDER'], image.filename)

        # Verify the image file exists
        if not os.path.exists(image_path):
            return jsonify({
                'error': f'Image file not found: {image.filename}'
            }), 404

        # Use AsyncAIProcessor to start real async processing
        job_id = AsyncAIProcessor.start_ai_generation(
            image_id=image.id,
            user_id=current_user.id,
            image_path=image_path
        )

        return jsonify({
            'success': True,
            'job_id': job_id,
            'message': 'AI content generation started',
            'status_url': url_for('image.get_ai_job_status', job_id=job_id),
            'already_running': False
        })

    except Exception as e:
        return jsonify({
            'error': f'Failed to start AI content generation: {str(e)}'
        }), 500

@image_bp.route('/ai-job/<job_id>/status', methods=['GET'])
@login_required
def get_ai_job_status(job_id):
    """Get the status of an AI generation job with simulated processing"""
    try:
        job = AIGenerationJob.query.get(job_id)

        if not job:
            return jsonify({'error': 'Job not found'}), 404

        # Check if user is authorized to view this job
        if job.user_id != current_user.id and not current_user.is_admin:
            return jsonify({'error': 'You are not authorized to view this job'}), 403

        # Simply return the current job status without simulation
        # The actual processing is handled by AsyncAIProcessor in background thread
        return jsonify({
            'success': True,
            'job': job.to_dict()
        })

    except Exception as e:
        return jsonify({
            'error': f'Failed to get job status: {str(e)}'
        }), 500



@image_bp.route('/ai-job/<job_id>/cancel', methods=['POST'])
@login_required
def cancel_ai_job(job_id):
    """Cancel a running AI generation job"""
    try:
        job = AIGenerationJob.query.get(job_id)

        if not job:
            return jsonify({'error': 'Job not found'}), 404

        # Check if user is authorized to cancel this job
        if job.user_id != current_user.id and not current_user.is_admin:
            return jsonify({'error': 'You are not authorized to cancel this job'}), 403

        # Only allow canceling pending or processing jobs
        if job.status not in [AIJobStatus.PENDING, AIJobStatus.PROCESSING]:
            return jsonify({'error': 'Job cannot be cancelled in its current state'}), 400

        # Update job status to cancelled (we can use FAILED status for this)
        job.update_status(
            AIJobStatus.FAILED,
            progress=0,
            message="Job cancelled by user",
            error="Cancelled by user request"
        )

        return jsonify({
            'success': True,
            'message': 'Job cancelled successfully'
        })

    except Exception as e:
        return jsonify({
            'error': f'Failed to cancel job: {str(e)}'
        }), 500

@image_bp.route('/<int:id>/delete', methods=['GET', 'POST'])
@login_required
def delete(id):
    print(f"Delete route called for image ID: {id}")
    image = Image.query.get_or_404(id)
    print(f"Found image: {image.filename}")

    # Check if user is authorized to delete this image
    if image.user_id != current_user.id and not current_user.is_admin:
        print(f"User not authorized: user_id={current_user.id}, image.user_id={image.user_id}")
        flash('You are not authorized to delete this image', 'danger')
        return redirect(url_for('timeline.view', id=image.timeline_id))

    timeline_id = image.timeline_id
    print(f"Timeline ID: {timeline_id}")

    # Delete the file from the filesystem
    try:
        file_path = os.path.join(current_app.config['UPLOAD_FOLDER'], image.filename)
        print(f"Deleting file: {file_path}")
        if os.path.exists(file_path):
            os.remove(file_path)
            print(f"File deleted successfully")
        else:
            print(f"File does not exist: {file_path}")
    except Exception as e:
        print(f"Error deleting file: {str(e)}")
        flash(f'Error deleting image file: {str(e)}', 'warning')

    # Delete the database record
    print(f"Deleting image from database")
    db.session.delete(image)
    db.session.commit()
    print(f"Image deleted from database")

    flash('Image deleted successfully!', 'success')
    return redirect(url_for('timeline.view', id=timeline_id))
