import click
from flask.cli import with_appcontext
from datetime import date
from app import db
from app.models import User, Company, Timeline, Image


def register_commands(app):
    """Register CLI commands with the Flask app."""

    @app.cli.command('create-admin')
    @click.argument('username')
    @click.argument('email')
    @click.argument('password')
    @with_appcontext
    def create_admin(username, email, password):
        """Create an admin user."""
        user = User.query.filter_by(username=username).first()
        if user:
            click.echo(f'User {username} already exists')
            return

        # Create default company if none exists
        company = Company.query.first()
        if not company:
            company = Company(name='Default Company', description='Default company for the system')
            db.session.add(company)
            db.session.commit()
            click.echo('Created default company')

        user = User(username=username, email=email, is_admin=True, company_id=company.id)
        user.set_password(password)
        db.session.add(user)
        db.session.commit()
        click.echo(f'Created admin user: {username}')

    @app.cli.command('init-db')
    @with_appcontext
    def init_db():
        """Initialize the database with sample data."""
        # Create all tables
        db.create_all()

        # Create companies
        companies = [
            {'name': 'Acme Corp', 'description': 'A global technology company'},
            {'name': 'Stark Industries', 'description': 'Leading defense contractor'},
            {'name': 'Wayne Enterprises', 'description': 'Multinational conglomerate'}
        ]

        for company_data in companies:
            company = Company.query.filter_by(name=company_data['name']).first()
            if not company:
                company = Company(**company_data)
                db.session.add(company)

        db.session.commit()
        click.echo('Created sample companies')

        # Create admin user if it doesn't exist
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                company_id=Company.query.first().id,
                is_admin=True
            )
            admin.set_password('password')
            db.session.add(admin)
            db.session.commit()
            click.echo('Created admin user')

        # Create sample timelines
        if Timeline.query.count() == 0:
            timelines = [
                {
                    'title': 'Project Alpha Launch',
                    'work_date': date(2023, 5, 15),
                    'description': 'Documentation of the Project Alpha launch day',
                    'business_content': """
# Project Alpha Launch

## Overview
Today we successfully launched Project Alpha to our first set of customers.

## Key Activities
- Deployed the application to production servers
- Conducted final QA testing
- Sent welcome emails to initial users
- Monitored system performance

## Results
The launch was successful with 95% uptime and positive initial feedback.
                    """,
                    'user_id': admin.id,
                    'company_id': Company.query.first().id
                },
                {
                    'title': 'Quarterly Planning',
                    'work_date': date(2023, 6, 1),
                    'description': 'Q3 planning session with the team',
                    'business_content': """
# Q3 Planning Session

## Agenda
1. Review Q2 results
2. Set Q3 objectives
3. Resource allocation
4. Risk assessment

## Decisions Made
- Prioritized mobile app development
- Allocated budget for new marketing campaign
- Scheduled mid-quarter review for August 15
                    """,
                    'user_id': admin.id,
                    'company_id': Company.query.first().id
                }
            ]

            for timeline_data in timelines:
                timeline = Timeline(**timeline_data)
                db.session.add(timeline)

            db.session.commit()
            click.echo('Created sample timelines')

        click.echo('Database initialized with sample data')
