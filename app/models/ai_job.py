from app import db
from datetime import datetime
from enum import Enum

class AIJobStatus(Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    TIMEOUT = "timeout"

class AIGenerationJob(db.Model):
    """Model to track AI content generation jobs"""
    __tablename__ = 'ai_generation_job'
    
    id = db.Column(db.String(36), primary_key=True)  # UUID
    image_id = db.Column(db.Integer, db.<PERSON>ey('image.id'), nullable=False)
    user_id = db.Column(db.Integer, db.<PERSON>Key('user.id'), nullable=False)
    status = db.Column(db.Enum(AIJobStatus), default=AIJobStatus.PENDING, nullable=False)
    progress = db.Column(db.Integer, default=0)  # 0-100
    status_message = db.Column(db.String(255), default="Initializing...")
    result_content = db.Column(db.Text)  # Generated AI content
    error_message = db.Column(db.Text)  # Error details if failed
    started_at = db.Column(db.DateTime)
    completed_at = db.Column(db.DateTime)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    image = db.relationship('Image', backref='ai_jobs')
    user = db.relationship('User', backref='ai_jobs')
    
    def __repr__(self):
        return f'<AIGenerationJob {self.id} - {self.status.value}>'
    
    def to_dict(self):
        """Convert job to dictionary for JSON response"""
        return {
            'id': self.id,
            'image_id': self.image_id,
            'status': self.status.value,
            'progress': self.progress,
            'status_message': self.status_message,
            'result_content': self.result_content,
            'error_message': self.error_message,
            'started_at': self.started_at.isoformat() if self.started_at else None,
            'completed_at': self.completed_at.isoformat() if self.completed_at else None,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }
    
    def update_status(self, status, progress=None, message=None, error=None):
        """Update job status with optional progress and message"""
        self.status = status
        if progress is not None:
            self.progress = progress
        if message is not None:
            self.status_message = message
        if error is not None:
            self.error_message = error
        
        if status == AIJobStatus.PROCESSING and not self.started_at:
            self.started_at = datetime.utcnow()
        elif status in [AIJobStatus.COMPLETED, AIJobStatus.FAILED, AIJobStatus.TIMEOUT]:
            self.completed_at = datetime.utcnow()
        
        self.updated_at = datetime.utcnow()
        db.session.commit()
