import threading
import time
import os
import uuid
from datetime import datetime, <PERSON><PERSON><PERSON>
from PIL import Image as PILImage
from flask import current_app
from app import db
from app.models.ai_job import AIGenerationJob, AIJobStatus
# IMPORT FOR AI
import google.generativeai as genai
import io
import base64
from pathlib import Path
import requests
from google.oauth2 import service_account
import os
from dotenv import load_dotenv
import logging

load_dotenv()

class GeminiGoogleService:
    def __init__(self, credentials_path: str):
        self.model = None
        self.credentials_path = credentials_path
        self.model_name = os.environ.get("GEMINI_MODEL_PROCESS", "gemini-2.5-pro-preview-05-06")
        self.prompt = ""
        self.init_data()
    def init_data(self):
        self.model = self.setup_gemini_with_credentials()
        self.prompt = """Analyze the content in the user's image according to the following criteria:
- Which screen is the user opening, which code language, which project.
- Which IDE is the user using.
- Which role is the user performing the work.
- Analyze and evaluate the user's ability to focus on work.
Output returned in markdown format.
        """
    def setup_gemini_with_credentials(self):
        """
        Set up Gemini API using service account credentials file

        Args:
            credentials_path (str): Path to your service account JSON file

        Returns:
            GenerativeModel: Configured Gemini model
        """
        try:
            # Load service account credentials
            credentials = service_account.Credentials.from_service_account_file(
                self.credentials_path,
                scopes=['https://www.googleapis.com/auth/generative-language']
            )

            # Configure genai with credentials
            genai.configure(credentials=credentials)

            return genai.GenerativeModel(self.model_name)

        except Exception as e:
            logging.error(f"Error setting up credentials: {str(e)}")
            return None
    def process_image(self, image_path):
        """
        Analyze a local image file
        """
        try:
            # Load image
            if self.model is None:
                logging.error("Model not initialized")
                return None
            img = PILImage.open(image_path)

            # Generate content
            response = self.model.generate_content([self.prompt, img])
            return response.text
        except Exception as e:
            logging.error(f"Error processing image: {str(e)}")
            return f"Error analyzing local image: {str(e)}"
class AsyncAIProcessor:
    """Handles asynchronous AI content generation for images"""
    def __init__(self):
        self.credentials_path = os.environ.get("GEMINI_AUTH_CREDENTIALS", "")
        self.gemini_service = GeminiGoogleService(self.credentials_path)

    @staticmethod
    def start_ai_generation(image_id, user_id, image_path):
        """
        Start asynchronous AI content generation for an image

        Args:
            image_id (int): ID of the image to process
            user_id (int): ID of the user requesting the generation
            image_path (str): Full path to the image file

        Returns:
            str: Job ID for tracking the generation process
        """
        # Generate unique job ID
        job_id = str(uuid.uuid4())

        # Create job record
        job = AIGenerationJob(
            id=job_id,
            image_id=image_id,
            user_id=user_id,
            status=AIJobStatus.PENDING,
            progress=0,
            status_message="Initializing AI analysis..."
        )

        db.session.add(job)
        db.session.commit()

        # Start background thread for processing
        thread = threading.Thread(
            target=AsyncAIProcessor._process_image_async,
            args=(job_id, image_path),
            daemon=True
        )
        thread.start()

        return job_id

    @staticmethod
    def _process_image_async(job_id, image_path):
        """
        Background thread function to process image with AI

        Args:
            job_id (str): Job ID to track progress
            image_path (str): Path to the image file
        """
        # Import here to avoid circular imports
        from app import create_app

        # Create application context for the background thread
        app = create_app()
        with app.app_context():
            try:
                # Get job from database
                job = AIGenerationJob.query.get(job_id)
                if not job:
                    return

                # Update status to processing
                job.update_status(
                    AIJobStatus.PROCESSING,
                    progress=10,
                    message="Analyzing image properties..."
                )

                # Simulate processing steps with progress updates
                AsyncAIProcessor._simulate_ai_processing(job, image_path)

            except Exception as e:
                # Handle any errors
                try:
                    job = AIGenerationJob.query.get(job_id)
                    if job:
                        job.update_status(
                            AIJobStatus.FAILED,
                            progress=0,
                            message="AI processing failed",
                            error=str(e)
                        )
                except Exception as db_error:
                    print(f"Failed to update job status: {db_error}")

    @staticmethod
    def _simulate_ai_processing(job, image_path):
        """
        Simulate AI processing with realistic steps and timing

        Args:
            job (AIGenerationJob): Job object to update
            image_path (str): Path to the image file
        """
        try:
            # Step 1: Validate image file
            job.update_status(
                AIJobStatus.PROCESSING,
                progress=20,
                message="Validating image file..."
            )
            time.sleep(0.5)  # Simulate processing time

            if not os.path.exists(image_path):
                raise FileNotFoundError(f"Image file not found: {image_path}")

            # Step 2: Load and analyze image properties
            job.update_status(
                AIJobStatus.PROCESSING,
                progress=40,
                message="Loading image and extracting metadata..."
            )
            time.sleep(1.0)

            img = PILImage.open(image_path)
            width, height = img.size
            file_size = os.path.getsize(image_path)

            # Step 3: Simulate AI analysis
            job.update_status(
                AIJobStatus.PROCESSING,
                progress=60,
                message="Running AI analysis on image content..."
            )
            time.sleep(2.0)  # Simulate AI processing time

            # Step 4: Generate content
            job.update_status(
                AIJobStatus.PROCESSING,
                progress=80,
                message="Generating detailed content analysis..."
            )
            time.sleep(1.5)

            # Create comprehensive AI content using actual AI service
            processor = AsyncAIProcessor()  # This calls the __init__ method
            ai_content = processor._generate_detailed_content(
                image_path, width, height, file_size, img.format, img.mode
            )

            # Step 5: Finalize
            job.update_status(
                AIJobStatus.PROCESSING,
                progress=95,
                message="Finalizing analysis..."
            )
            time.sleep(0.5)

            # Complete the job
            job.result_content = ai_content
            job.update_status(
                AIJobStatus.COMPLETED,
                progress=100,
                message="AI analysis completed successfully!"
            )

        except Exception as e:
            job.update_status(
                AIJobStatus.FAILED,
                progress=0,
                message="Processing failed",
                error=str(e)
            )

    def _generate_detailed_content(self, image_path, width, height, file_size, img_format, img_mode):
        """
        Generate detailed AI content based on image analysis

        Args:
            image_path (str): Path to the image
            width (int): Image width
            height (int): Image height
            file_size (int): File size in bytes
            img_format (str): Image format
            img_mode (str): Image mode

        Returns:
            str: Generated AI content in markdown format
        """
        filename = os.path.basename(image_path)
        file_size_mb = file_size / (1024 * 1024)

        # Determine image characteristics
        resolution_category = "High" if width * height > 1000000 else "Standard" if width * height > 300000 else "Low"
        aspect_ratio = width / height
        orientation = "Landscape" if aspect_ratio > 1.3 else "Portrait" if aspect_ratio < 0.7 else "Square"

        # Generate timestamp
        analysis_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Use actual AI service to process the image
        ai_analysis = ""
        try:
            if self.gemini_service and self.gemini_service.model:
                ai_analysis = self.gemini_service.process_image(image_path)
                if ai_analysis:
                    ai_analysis = f"\n\n## AI Content Analysis\n\n{ai_analysis}\n"
        except Exception as e:
            ai_analysis = f"\n\n## AI Content Analysis\n\n*AI analysis failed: {str(e)}*\n"

        return ai_analysis

    @staticmethod
    def get_job_status(job_id):
        """
        Get the current status of an AI generation job

        Args:
            job_id (str): Job ID to check

        Returns:
            dict: Job status information or None if not found
        """
        job = AIGenerationJob.query.get(job_id)
        return job.to_dict() if job else None

    @staticmethod
    def cleanup_old_jobs(days_old=7):
        """
        Clean up old completed/failed jobs

        Args:
            days_old (int): Remove jobs older than this many days
        """
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        old_jobs = AIGenerationJob.query.filter(
            AIGenerationJob.created_at < cutoff_date,
            AIGenerationJob.status.in_([AIJobStatus.COMPLETED, AIJobStatus.FAILED, AIJobStatus.TIMEOUT])
        ).all()

        for job in old_jobs:
            db.session.delete(job)

        db.session.commit()
        return len(old_jobs)

