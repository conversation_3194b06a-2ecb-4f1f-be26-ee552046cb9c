#!/usr/bin/env python3

import os
import sys
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User, Company, Timeline, Image
from datetime import datetime, date

def init_db():
    """Initialize the database with sample data."""
    app = create_app()
    with app.app_context():
        # Create all tables
        db.create_all()

        # Create companies
        companies = [
            {'name': 'Acme Corp', 'description': 'A global technology company'},
            {'name': 'Stark Industries', 'description': 'Leading defense contractor'},
            {'name': 'Wayne Enterprises', 'description': 'Multinational conglomerate'}
        ]

        for company_data in companies:
            company = Company.query.filter_by(name=company_data['name']).first()
            if not company:
                company = Company(**company_data)
                db.session.add(company)

        db.session.commit()
        print('Created sample companies')

        # Create admin user if it doesn't exist
        admin = User.query.filter_by(username='admin').first()
        if not admin:
            admin = User(
                username='admin',
                email='<EMAIL>',
                company_id=Company.query.first().id,
                is_admin=True
            )
            admin.set_password('password')
            db.session.add(admin)
            db.session.commit()
            print('Created admin user')

        # Create sample timelines
        if Timeline.query.count() == 0:
            timelines = [
                {
                    'title': 'Project Alpha Launch',
                    'work_date': date(2023, 5, 15),
                    'description': 'Documentation of the Project Alpha launch day',
                    'business_content': """
# Project Alpha Launch

## Overview
Today we successfully launched Project Alpha to our first set of customers.

## Key Activities
- Deployed the application to production servers
- Conducted final QA testing
- Sent welcome emails to initial users
- Monitored system performance

## Results
The launch was successful with 95% uptime and positive initial feedback.
                    """,
                    'user_id': admin.id,
                    'company_id': Company.query.first().id
                },
                {
                    'title': 'Quarterly Planning',
                    'work_date': date(2023, 6, 1),
                    'description': 'Q3 planning session with the team',
                    'business_content': """
# Q3 Planning Session

## Agenda
1. Review Q2 results
2. Set Q3 objectives
3. Resource allocation
4. Risk assessment

## Decisions Made
- Prioritized mobile app development
- Allocated budget for new marketing campaign
- Scheduled mid-quarter review for August 15
                    """,
                    'user_id': admin.id,
                    'company_id': Company.query.first().id
                }
            ]

            for timeline_data in timelines:
                timeline = Timeline(**timeline_data)
                db.session.add(timeline)

            db.session.commit()
            print('Created sample timelines')

        print('Database initialized with sample data')

if __name__ == '__main__':
    init_db()
