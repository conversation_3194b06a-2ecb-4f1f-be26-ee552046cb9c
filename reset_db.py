#!/usr/bin/env python3

import os
import shutil
import subprocess
import sys

def run_command(command):
    """Run a shell command and return success status"""
    print(f"Running: {command}")
    result = subprocess.run(command, shell=True, capture_output=True, text=True)
    if result.returncode == 0:
        print(f"✅ Success: {command}")
        if result.stdout:
            print(result.stdout)
    else:
        print(f"❌ Error: {command}")
        if result.stderr:
            print(result.stderr)
        return False
    return True

def reset_database():
    """Reset the database and migrations completely"""
    print("🔄 Resetting database and migrations...")
    
    # Remove existing database
    if os.path.exists('app.db'):
        os.remove('app.db')
        print("✅ Removed existing database")
    
    # Remove migrations directory
    if os.path.exists('migrations'):
        shutil.rmtree('migrations')
        print("✅ Removed migrations directory")
    
    # Run the migration sequence
    commands = [
        "flask db init",
        "flask db migrate -m 'Initial migration'",
        "flask db upgrade",
        "flask init-db"
    ]
    
    for command in commands:
        if not run_command(command):
            print(f"❌ Failed at: {command}")
            sys.exit(1)
    
    print("\n🎉 Database reset completed successfully!")
    print("📋 Summary:")
    print("   - Database: app.db (created)")
    print("   - Migrations: migrations/ (created)")
    print("   - Sample data: loaded")
    print("   - Admin user: admin/password")

if __name__ == '__main__':
    reset_database()
